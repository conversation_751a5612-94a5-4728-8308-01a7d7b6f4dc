# 🎮 H5游戏关键词猎手

一个自动化收集H5游戏关键词并同步到Notion的可视化工具。

## ✨ 功能特性

- 🔍 **多平台关键词收集**: 自动从Reddit、YouTube等平台收集热门游戏关键词
- 📈 **趋势分析**: 集成Google Trends分析关键词7天、30天趋势
- 🔎 **SEO分析**: 分析SERP结果、计算KGR值、检查域名可用性
- 📝 **Notion同步**: 自动将分析结果同步到Notion数据库
- 🖥️ **可视化界面**: 现代化的Web界面，实时监控和管理
- ⚡ **自动化运行**: 支持定时任务和手动触发

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- 网络连接

### 2. 安装

```bash
# 克隆或下载项目
cd h5gamekeywordhunter

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置API密钥

编辑 `config.json` 文件，填入您的API密钥：

```json
{
  "reddit": {
    "client_id": "您的Reddit客户端ID",
    "client_secret": "您的Reddit客户端密钥",
    "user_agent": "H5GameKeywordHunter/1.0"
  },
  "youtube": {
    "api_key": "您的YouTube API密钥"
  },
  "notion": {
    "token": "您的Notion集成令牌",
    "database_id": "您的Notion数据库ID"
  },
  "google_search": {
    "api_key": "您的Google搜索API密钥",
    "search_engine_id": "您的自定义搜索引擎ID"
  }
}
```

### 4. 运行应用

```bash
python run_app.py
```

应用将自动在浏览器中打开，访问地址: http://localhost:8501

## 🔧 API密钥获取指南

### Reddit API
1. 访问 [Reddit App Preferences](https://www.reddit.com/prefs/apps)
2. 点击 "Create App" 或 "Create Another App"
3. 选择 "script" 类型
4. 获取 `client_id` 和 `client_secret`

### YouTube API
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 YouTube Data API v3
4. 创建API密钥

### Notion API
1. 访问 [Notion Developers](https://www.notion.so/my-integrations)
2. 创建新的集成
3. 获取 `Internal Integration Token`
4. 在Notion中创建数据库并共享给集成

### Google Custom Search API
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 启用 Custom Search API
3. 创建API密钥
4. 在 [Custom Search Engine](https://cse.google.com/) 创建搜索引擎

## 📊 Notion数据库结构

在Notion中创建数据库，包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| 关键词 | Title | 主键，关键词内容 |
| 来源平台 | Select | Reddit, YouTube等 |
| 采集日期 | Date | 数据采集日期 |
| 7天趋势评分 | Number | Google Trends 7天评分 |
| 30天趋势评分 | Number | Google Trends 30天评分 |
| Google Trends 链接 | URL | 趋势分析链接 |
| 是否已被注册域名 | Checkbox | 域名注册状态 |
| SERP 是否有图片 | Checkbox | 搜索结果是否包含图片 |
| SERP 是否有大站 | Checkbox | 搜索结果是否有大型网站 |
| allintitle 数值 | Number | 完全匹配标题的结果数 |
| KGR 值 | Number | 关键词黄金比例 |
| 备注 | Text | 附加信息 |

## 🎯 使用指南

### 主要功能

1. **仪表板**: 查看收集统计、趋势图表
2. **关键词列表**: 浏览和过滤收集的关键词
3. **趋势分析**: 查看关键词趋势对比
4. **设置**: 管理配置和系统信息

### 操作流程

1. **验证配置**: 在侧边栏点击"验证配置"
2. **开始收集**: 点击"开始收集"启动自动收集
3. **查看结果**: 在各个标签页查看收集结果
4. **同步Notion**: 点击"同步到Notion"保存数据

### 手动操作

- **立即收集一次**: 手动触发一次完整的数据收集
- **同步到Notion**: 将当前数据同步到Notion数据库

## 📁 项目结构

```
h5gamekeywordhunter/
├── main.py                 # 主应用程序
├── config.py              # 配置管理
├── config.json            # 配置文件
├── notion_sync.py         # Notion同步模块
├── run_app.py             # 启动脚本
├── requirements.txt       # 依赖包列表
├── collectors/            # 数据收集器
│   ├── reddit_collector.py
│   └── youtube_collector.py
├── analyzers/             # 分析器
│   ├── trends_analyzer.py
│   └── seo_analyzer.py
└── README.md             # 说明文档
```

## 🔍 监控平台

### Reddit
- 监控子版块: WebGames, browsergames, gaming等
- 提取游戏相关关键词
- 分析帖子热度和评论

### YouTube
- 搜索游戏相关视频
- 分析视频标题和描述
- 获取观看量和互动数据

## ⚙️ 高级配置

### 自定义监控关键词

在 `config.json` 中修改 `monitoring.keywords_to_track`:

```json
"keywords_to_track": [
  "addictive game",
  "browser game",
  "html5 game",
  "自定义关键词"
]
```

### 调整收集频率

修改 `monitoring` 配置:

```json
"monitoring": {
  "max_posts_per_subreddit": 20,
  "max_youtube_videos": 50
}
```

## 🐛 故障排除

### 常见问题

1. **API连接失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 查看API配额是否用完

2. **Notion同步失败**
   - 确认数据库ID正确
   - 检查集成权限
   - 验证数据库字段结构

3. **数据收集为空**
   - 检查关键词配置
   - 确认平台API正常
   - 查看日志文件

### 日志文件

应用运行时会生成 `app.log` 日志文件，包含详细的运行信息和错误信息。

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 支持Reddit和YouTube数据收集
- 集成Google Trends分析
- Notion自动同步功能
- Web可视化界面

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

MIT License

## 📞 支持

如有问题，请查看文档或提交Issue。
