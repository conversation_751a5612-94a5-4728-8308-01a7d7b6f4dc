# H5游戏关键词猎手 - 演示版

## 🎮 项目简介

H5游戏关键词猎手是一个专门为H5游戏开发者和营销人员设计的关键词发现和分析工具。这个演示版本展示了主要功能，使用模拟数据，无需配置外部API。

## ✨ 主要功能

- **关键词收集**: 模拟从多个平台收集游戏相关关键词
- **趋势分析**: 展示关键词热度变化趋势
- **SEO分析**: 计算KGR值，评估关键词竞争难度
- **数据可视化**: 直观展示关键词数据和趋势
- **交互式界面**: 基于Streamlit的现代化Web界面

## 🚀 快速开始

### 方法1: 使用批处理文件（推荐）
双击 `start_demo.bat` 文件即可启动演示版本。

### 方法2: 命令行启动
```bash
# 进入项目目录
cd h5gamekeywordhunter

# 启动演示版本
python -m streamlit run demo.py --server.port 8502
```

应用将在浏览器中自动打开：http://localhost:8502

## 📋 系统要求

- Python 3.8+
- 已安装的依赖包：
  - streamlit
  - pandas
  - plotly
  - numpy

## 📦 安装依赖

如果缺少依赖包，请运行：
```bash
pip install streamlit pandas plotly numpy
```

## 🎯 功能说明

### 1. 数据仪表板
- 显示关键词统计指标
- 可视化数据分布
- 趋势分析图表

### 2. 关键词列表
- 查看所有收集的关键词
- 支持过滤和排序
- 详细的关键词信息

### 3. 趋势分析
- 7天 vs 30天趋势对比
- 热门关键词排行
- 竞争度分析

### 4. 关于页面
- 项目介绍
- 技术栈说明
- 完整版本信息

## 🔧 演示功能

- **生成新数据**: 点击侧边栏的"生成新数据"按钮
- **添加关键词**: 点击"添加随机关键词"按钮
- **数据过滤**: 使用侧边栏的过滤选项
- **排序功能**: 在关键词列表页面选择排序方式

## 🌟 完整版本

完整版本支持以下额外功能：

- **Reddit API集成**: 从游戏相关subreddit收集真实关键词
- **YouTube API集成**: 分析热门游戏视频标题
- **Google Trends分析**: 获取真实的关键词趋势数据
- **Notion数据库同步**: 自动保存分析结果
- **SEO关键词分析**: 真实的KGR值计算和竞争度分析
- **定时任务**: 自动化关键词收集

### 完整版本安装
```bash
pip install praw notion-client pytrends google-api-python-client python-dotenv schedule
```

### API配置
完整版本需要配置以下API密钥：
- Reddit API (client_id, client_secret)
- YouTube Data API (api_key)  
- Notion API (token, database_id)
- Google Custom Search API (可选)

## 📞 技术支持

如需完整版本或技术支持，请联系开发团队。

## 📄 许可证

本项目仅供学习和演示使用。
