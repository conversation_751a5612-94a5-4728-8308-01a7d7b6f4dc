# Windows 用户设置指南

## 🚨 解决编码问题

如果您在Windows上遇到编码问题（如乱码），请按照以下步骤操作：

### 方法1: 使用Python脚本启动（推荐）

```bash
# 直接运行Python脚本，避免批处理文件的编码问题
python start.py
```

### 方法2: 使用改进的批处理文件

```bash
# 使用新的批处理文件
start_app.bat
```

### 方法3: 直接启动Streamlit

```bash
# 如果上述方法都有问题，直接运行
streamlit run main.py
```

## 📋 完整设置步骤

### 1. 检查Python安装
```bash
python --version
```
确保显示Python 3.8或更高版本。

### 2. 快速测试
```bash
python quick_test.py
```
这会检查所有必需的文件和依赖。

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 配置API密钥
```bash
python setup_config.py
```
按照向导提示输入您的API密钥。

### 5. 测试组件
```bash
python test_components.py
```
验证所有组件是否正常工作。

### 6. 启动应用
选择以下任一方式：

**方式A: Python脚本（推荐）**
```bash
python start.py
```

**方式B: 批处理文件**
```bash
start.bat
```

**方式C: 直接启动**
```bash
streamlit run main.py
```

## 🔧 常见问题解决

### 问题1: 编码错误/乱码
**解决方案:**
- 使用 `python start.py` 而不是批处理文件
- 确保命令行支持UTF-8编码

### 问题2: Python未找到
**解决方案:**
- 确保Python已安装并添加到PATH
- 尝试使用 `py` 命令代替 `python`

### 问题3: 模块未找到
**解决方案:**
```bash
pip install -r requirements.txt
```

### 问题4: Streamlit未找到
**解决方案:**
```bash
pip install streamlit
```

### 问题5: 配置文件错误
**解决方案:**
```bash
python setup_config.py
```

## 📱 访问应用

启动成功后，在浏览器中访问：
```
http://localhost:8501
```

## 🛠️ 高级设置

### 修改端口
如果8501端口被占用，可以修改启动命令：
```bash
streamlit run main.py --server.port 8502
```

### 允许外部访问
```bash
streamlit run main.py --server.address 0.0.0.0
```

### 禁用自动打开浏览器
```bash
streamlit run main.py --server.headless true
```

## 📞 获取帮助

如果仍有问题：

1. **运行诊断:**
   ```bash
   python quick_test.py
   ```

2. **查看详细测试:**
   ```bash
   python test_components.py
   ```

3. **检查日志:**
   查看生成的 `app.log` 文件

4. **重新配置:**
   ```bash
   python setup_config.py
   ```

## 💡 提示

- 建议使用 `python start.py` 启动，避免编码问题
- 确保在项目根目录下运行命令
- 如果遇到权限问题，尝试以管理员身份运行
- 定期更新依赖包：`pip install -r requirements.txt --upgrade`

---

**如果您成功启动了应用，恭喜！现在可以开始使用H5游戏关键词猎手了。** 🎉
