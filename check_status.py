#!/usr/bin/env python3
"""
H5游戏关键词猎手 - 状态查看器
"""

import subprocess
import requests
import json
import os
import sys
from datetime import datetime

def print_header():
    """打印标题"""
    print("=" * 50)
    print("    🎮 H5游戏关键词猎手 状态查看器")
    print("=" * 50)
    print()

def check_port_status():
    """检查端口状态"""
    print("📍 [1] 检查端口8501状态:")
    try:
        result = subprocess.run(
            ["netstat", "-an"], 
            capture_output=True, 
            text=True, 
            shell=True
        )
        if ":8501" in result.stdout:
            print("    ✅ 端口8501正在使用中 - 应用正在运行")
            return True
        else:
            print("    ❌ 端口8501未被占用 - 应用可能已停止")
            return False
    except Exception as e:
        print(f"    ⚠️ 无法检查端口状态: {e}")
        return False

def check_python_processes():
    """检查Python进程"""
    print("\n🐍 [2] 检查Python进程:")
    try:
        result = subprocess.run(
            ["tasklist", "/fi", "imagename eq python.exe"], 
            capture_output=True, 
            text=True, 
            shell=True
        )
        if "python.exe" in result.stdout:
            lines = result.stdout.split('\n')
            python_lines = [line for line in lines if "python.exe" in line]
            print(f"    ✅ 发现 {len(python_lines)} 个Python进程正在运行")
            return True
        else:
            print("    ❌ 未发现Python进程")
            return False
    except Exception as e:
        print(f"    ⚠️ 无法检查Python进程: {e}")
        return False

def check_app_response():
    """检查应用响应"""
    print("\n🌐 [3] 检查应用响应:")
    try:
        response = requests.get("http://localhost:8501", timeout=5)
        if response.status_code == 200:
            print("    ✅ 应用响应正常")
            return True
        else:
            print(f"    ⚠️ 应用响应异常 (状态码: {response.status_code})")
            return False
    except requests.exceptions.ConnectionError:
        print("    ❌ 无法连接到应用")
        return False
    except requests.exceptions.Timeout:
        print("    ⚠️ 应用响应超时")
        return False
    except Exception as e:
        print(f"    ⚠️ 检查应用响应时出错: {e}")
        return False

def check_config_file():
    """检查配置文件"""
    print("\n📄 [4] 检查配置文件:")
    if os.path.exists("config.json"):
        try:
            with open("config.json", "r", encoding="utf-8") as f:
                config = json.load(f)
            print("    ✅ 配置文件存在且格式正确")
            
            # 检查关键配置
            apis = ["reddit", "youtube", "notion", "google_search"]
            configured_apis = []
            for api in apis:
                if api in config and config[api]:
                    configured_apis.append(api)
            
            print(f"    📊 已配置的API: {', '.join(configured_apis)}")
            return True
        except Exception as e:
            print(f"    ❌ 配置文件格式错误: {e}")
            return False
    else:
        print("    ❌ 配置文件不存在")
        return False

def show_status_summary(port_ok, python_ok, app_ok, config_ok):
    """显示状态总结"""
    print("\n" + "=" * 50)
    print("📊 状态总结:")
    print("-" * 30)
    
    total_checks = 4
    passed_checks = sum([port_ok, python_ok, app_ok, config_ok])
    
    status_items = [
        ("端口状态", port_ok),
        ("Python进程", python_ok),
        ("应用响应", app_ok),
        ("配置文件", config_ok)
    ]
    
    for item, status in status_items:
        icon = "✅" if status else "❌"
        print(f"    {icon} {item}")
    
    print(f"\n📈 总体状态: {passed_checks}/{total_checks} 项检查通过")
    
    if passed_checks == total_checks:
        print("🎉 应用运行正常！")
    elif passed_checks >= 2:
        print("⚠️ 应用部分正常，可能需要重启")
    else:
        print("❌ 应用可能未启动或存在问题")

def show_quick_actions():
    """显示快速操作"""
    print("\n" + "=" * 50)
    print("🚀 快速操作:")
    print("-" * 30)
    print("    🌐 访问应用: http://localhost:8501")
    print("    🔄 启动应用: 双击 '启动应用.bat'")
    print("    📝 查看日志: 查看 'app_monitor.log' 文件")
    print("    ⚙️ 重新配置: 运行 'python setup_config.py'")

def main():
    """主函数"""
    print_header()
    
    # 执行各项检查
    port_ok = check_port_status()
    python_ok = check_python_processes()
    app_ok = check_app_response()
    config_ok = check_config_file()
    
    # 显示总结
    show_status_summary(port_ok, python_ok, app_ok, config_ok)
    show_quick_actions()
    
    print("\n" + "=" * 50)
    input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 再见！")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        input("按回车键退出...")
