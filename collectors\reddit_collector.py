import praw
import re
import logging
from typing import List, Dict, Any
from datetime import datetime
import time

class RedditCollector:
    def __init__(self, config):
        self.config = config
        self.reddit_config = config.get_reddit_config()
        self.monitoring_config = config.get_monitoring_config()
        self.reddit = None
        self.setup_reddit()
        
        # 游戏关键词模式
        self.game_patterns = [
            r'\b(?:game|gaming|play|playing)\b',
            r'\b(?:addictive|viral|trending|popular)\b',
            r'\b(?:browser|web|html5|online)\b',
            r'\b(?:like wordle|wordle-like|similar to)\b',
            r'\b(?:simple|easy|quick|casual)\b'
        ]
        
        # 排除词汇
        self.exclude_patterns = [
            r'\b(?:console|pc|steam|playstation|xbox)\b',
            r'\b(?:download|install|buy|purchase)\b',
            r'\b(?:aaa|triple-a|big budget)\b'
        ]
    
    def setup_reddit(self):
        """设置Reddit API连接"""
        try:
            self.reddit = praw.Reddit(
                client_id=self.reddit_config.get('client_id'),
                client_secret=self.reddit_config.get('client_secret'),
                user_agent=self.reddit_config.get('user_agent', 'H5GameKeywordHunter/1.0')
            )
            # 测试连接
            self.reddit.user.me()
            logging.info("Reddit API连接成功")
        except Exception as e:
            logging.error(f"Reddit API连接失败: {str(e)}")
            self.reddit = None
    
    def extract_keywords_from_text(self, text: str) -> List[str]:
        """从文本中提取游戏关键词"""
        if not text:
            return []
        
        text = text.lower()
        keywords = []
        
        # 检查是否包含游戏相关模式
        has_game_pattern = any(re.search(pattern, text, re.IGNORECASE) 
                              for pattern in self.game_patterns)
        
        if not has_game_pattern:
            return []
        
        # 检查是否包含排除模式
        has_exclude_pattern = any(re.search(pattern, text, re.IGNORECASE) 
                                 for pattern in self.exclude_patterns)
        
        if has_exclude_pattern:
            return []
        
        # 提取潜在的游戏名称
        # 查找引号中的内容
        quoted_matches = re.findall(r'"([^"]*)"', text)
        for match in quoted_matches:
            if len(match.split()) <= 4 and len(match) > 2:
                keywords.append(match.strip())
        
        # 查找"like X"模式
        like_matches = re.findall(r'like\s+([a-zA-Z0-9\s]{2,20})(?:\s|$|[,.!?])', text)
        for match in like_matches:
            clean_match = match.strip()
            if len(clean_match.split()) <= 3:
                keywords.append(clean_match)
        
        # 查找游戏名称模式
        game_name_patterns = [
            r'(?:game|playing)\s+([a-zA-Z0-9\s]{2,20})(?:\s|$|[,.!?])',
            r'([a-zA-Z0-9\s]{2,20})\s+(?:game|is addictive)',
            r'try\s+([a-zA-Z0-9\s]{2,20})(?:\s|$|[,.!?])'
        ]
        
        for pattern in game_name_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                clean_match = match.strip()
                if len(clean_match.split()) <= 3 and len(clean_match) > 2:
                    keywords.append(clean_match)
        
        # 清理和去重
        cleaned_keywords = []
        for keyword in keywords:
            keyword = keyword.strip().lower()
            if (len(keyword) > 2 and 
                len(keyword) < 50 and 
                keyword not in cleaned_keywords and
                not any(exclude in keyword for exclude in ['reddit', 'upvote', 'comment'])):
                cleaned_keywords.append(keyword)
        
        return cleaned_keywords[:5]  # 限制每个帖子最多5个关键词
    
    def collect_from_subreddit(self, subreddit_name: str, limit: int = 20) -> List[Dict[str, Any]]:
        """从指定subreddit收集关键词"""
        if not self.reddit:
            return []
        
        keywords_data = []
        
        try:
            subreddit = self.reddit.subreddit(subreddit_name)
            
            # 获取热门帖子
            for submission in subreddit.hot(limit=limit):
                # 从标题提取关键词
                title_keywords = self.extract_keywords_from_text(submission.title)
                
                # 从内容提取关键词
                content_keywords = []
                if hasattr(submission, 'selftext') and submission.selftext:
                    content_keywords = self.extract_keywords_from_text(submission.selftext)
                
                # 合并关键词
                all_keywords = list(set(title_keywords + content_keywords))
                
                for keyword in all_keywords:
                    keyword_data = {
                        'keyword': keyword,
                        'source_platform': 'Reddit',
                        'source_subreddit': subreddit_name,
                        'source_url': f"https://reddit.com{submission.permalink}",
                        'post_title': submission.title,
                        'post_score': submission.score,
                        'post_comments': submission.num_comments,
                        'collected_date': datetime.now().isoformat(),
                        'post_created': datetime.fromtimestamp(submission.created_utc).isoformat()
                    }
                    keywords_data.append(keyword_data)
                
                # 添加延迟避免API限制
                time.sleep(0.1)
                
        except Exception as e:
            logging.error(f"从subreddit {subreddit_name} 收集数据失败: {str(e)}")
        
        return keywords_data
    
    def collect_keywords(self) -> List[Dict[str, Any]]:
        """收集所有配置的subreddit的关键词"""
        if not self.reddit:
            logging.error("Reddit API未初始化")
            return []
        
        all_keywords = []
        subreddits = self.monitoring_config.get('reddit_subreddits', [])
        max_posts = self.monitoring_config.get('max_posts_per_subreddit', 20)
        
        logging.info(f"开始从 {len(subreddits)} 个subreddit收集关键词")
        
        for subreddit_name in subreddits:
            logging.info(f"正在收集 r/{subreddit_name}")
            keywords = self.collect_from_subreddit(subreddit_name, max_posts)
            all_keywords.extend(keywords)
            
            # 添加延迟避免API限制
            time.sleep(1)
        
        # 去重处理
        unique_keywords = {}
        for keyword_data in all_keywords:
            keyword = keyword_data['keyword']
            if keyword not in unique_keywords:
                unique_keywords[keyword] = keyword_data
            else:
                # 如果关键词已存在，保留评分更高的帖子
                if keyword_data['post_score'] > unique_keywords[keyword]['post_score']:
                    unique_keywords[keyword] = keyword_data
        
        result = list(unique_keywords.values())
        logging.info(f"Reddit收集完成，共获得 {len(result)} 个唯一关键词")
        
        return result
    
    def get_trending_keywords(self, timeframe: str = 'day') -> List[Dict[str, Any]]:
        """获取趋势关键词"""
        if not self.reddit:
            return []
        
        trending_keywords = []
        
        try:
            # 从r/all获取趋势内容
            for submission in self.reddit.subreddit('all').top(timeframe, limit=50):
                keywords = self.extract_keywords_from_text(submission.title)
                
                for keyword in keywords:
                    keyword_data = {
                        'keyword': keyword,
                        'source_platform': 'Reddit',
                        'source_subreddit': 'all',
                        'source_url': f"https://reddit.com{submission.permalink}",
                        'post_title': submission.title,
                        'post_score': submission.score,
                        'post_comments': submission.num_comments,
                        'collected_date': datetime.now().isoformat(),
                        'trending_timeframe': timeframe
                    }
                    trending_keywords.append(keyword_data)
                
                time.sleep(0.1)
                
        except Exception as e:
            logging.error(f"获取趋势关键词失败: {str(e)}")
        
        return trending_keywords
    
    def search_keywords(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """搜索特定关键词相关的帖子"""
        if not self.reddit:
            return []
        
        search_results = []
        
        try:
            for submission in self.reddit.subreddit('all').search(query, limit=limit, sort='hot'):
                keywords = self.extract_keywords_from_text(submission.title)
                
                for keyword in keywords:
                    keyword_data = {
                        'keyword': keyword,
                        'source_platform': 'Reddit',
                        'source_subreddit': submission.subreddit.display_name,
                        'source_url': f"https://reddit.com{submission.permalink}",
                        'post_title': submission.title,
                        'post_score': submission.score,
                        'post_comments': submission.num_comments,
                        'collected_date': datetime.now().isoformat(),
                        'search_query': query
                    }
                    search_results.append(keyword_data)
                
                time.sleep(0.1)
                
        except Exception as e:
            logging.error(f"搜索关键词失败: {str(e)}")
        
        return search_results
