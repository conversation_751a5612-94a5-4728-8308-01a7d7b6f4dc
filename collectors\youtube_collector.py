from googleapiclient.discovery import build
import re
import logging
from typing import List, Dict, Any
from datetime import datetime, timedelta
import time

class YouTubeCollector:
    def __init__(self, config):
        self.config = config
        self.youtube_config = config.get_youtube_config()
        self.monitoring_config = config.get_monitoring_config()
        self.youtube = None
        self.setup_youtube()
        
        # 游戏相关搜索词
        self.search_queries = [
            'browser game',
            'html5 game',
            'web game',
            'online game',
            'addictive game',
            'viral game',
            'simple game',
            'casual game',
            'io game',
            'wordle like game'
        ]
        
        # 游戏关键词模式
        self.game_patterns = [
            r'\b(?:game|gaming|play|playing)\b',
            r'\b(?:addictive|viral|trending|popular)\b',
            r'\b(?:browser|web|html5|online)\b',
            r'\b(?:like wordle|wordle-like|similar to)\b',
            r'\b(?:simple|easy|quick|casual)\b',
            r'\b(?:io|\.io)\b'
        ]
        
        # 排除词汇
        self.exclude_patterns = [
            r'\b(?:console|pc|steam|playstation|xbox)\b',
            r'\b(?:download|install|buy|purchase)\b',
            r'\b(?:aaa|triple-a|big budget)\b',
            r'\b(?:mobile app|app store|google play)\b'
        ]
    
    def setup_youtube(self):
        """设置YouTube API连接"""
        try:
            api_key = self.youtube_config.get('api_key')
            if not api_key or api_key == 'YOUR_YOUTUBE_API_KEY':
                logging.error("YouTube API密钥未配置")
                return
            
            self.youtube = build('youtube', 'v3', developerKey=api_key)
            logging.info("YouTube API连接成功")
        except Exception as e:
            logging.error(f"YouTube API连接失败: {str(e)}")
            self.youtube = None
    
    def extract_keywords_from_text(self, text: str) -> List[str]:
        """从文本中提取游戏关键词"""
        if not text:
            return []
        
        text = text.lower()
        keywords = []
        
        # 检查是否包含游戏相关模式
        has_game_pattern = any(re.search(pattern, text, re.IGNORECASE) 
                              for pattern in self.game_patterns)
        
        if not has_game_pattern:
            return []
        
        # 检查是否包含排除模式
        has_exclude_pattern = any(re.search(pattern, text, re.IGNORECASE) 
                                 for pattern in self.exclude_patterns)
        
        if has_exclude_pattern:
            return []
        
        # 提取潜在的游戏名称
        # 查找引号中的内容
        quoted_matches = re.findall(r'"([^"]*)"', text)
        for match in quoted_matches:
            if len(match.split()) <= 4 and len(match) > 2:
                keywords.append(match.strip())
        
        # 查找游戏名称模式
        game_name_patterns = [
            r'(?:playing|play)\s+([a-zA-Z0-9\s]{2,20})(?:\s|$|[,.!?])',
            r'([a-zA-Z0-9\s]{2,20})\s+(?:game|gameplay)',
            r'new\s+([a-zA-Z0-9\s]{2,20})(?:\s|$|[,.!?])',
            r'([a-zA-Z0-9\s]{2,20})\s+(?:is|was)\s+(?:addictive|viral|trending)'
        ]
        
        for pattern in game_name_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                clean_match = match.strip()
                if len(clean_match.split()) <= 3 and len(clean_match) > 2:
                    keywords.append(clean_match)
        
        # 查找.io游戏
        io_matches = re.findall(r'([a-zA-Z0-9]+)\.io', text)
        for match in io_matches:
            if len(match) > 2:
                keywords.append(f"{match}.io")
        
        # 清理和去重
        cleaned_keywords = []
        for keyword in keywords:
            keyword = keyword.strip().lower()
            if (len(keyword) > 2 and 
                len(keyword) < 50 and 
                keyword not in cleaned_keywords and
                not any(exclude in keyword for exclude in ['youtube', 'subscribe', 'like', 'comment'])):
                cleaned_keywords.append(keyword)
        
        return cleaned_keywords[:5]  # 限制每个视频最多5个关键词
    
    def search_videos(self, query: str, max_results: int = 25) -> List[Dict[str, Any]]:
        """搜索YouTube视频"""
        if not self.youtube:
            return []
        
        videos_data = []
        
        try:
            # 搜索视频
            search_response = self.youtube.search().list(
                q=query,
                part='id,snippet',
                maxResults=max_results,
                type='video',
                order='relevance',
                publishedAfter=(datetime.now() - timedelta(days=30)).isoformat() + 'Z'
            ).execute()
            
            video_ids = []
            for item in search_response['items']:
                video_ids.append(item['id']['videoId'])
            
            # 获取视频详细信息
            if video_ids:
                videos_response = self.youtube.videos().list(
                    part='snippet,statistics',
                    id=','.join(video_ids)
                ).execute()
                
                for video in videos_response['items']:
                    snippet = video['snippet']
                    statistics = video.get('statistics', {})
                    
                    # 从标题和描述提取关键词
                    title_keywords = self.extract_keywords_from_text(snippet.get('title', ''))
                    desc_keywords = self.extract_keywords_from_text(snippet.get('description', ''))
                    
                    all_keywords = list(set(title_keywords + desc_keywords))
                    
                    for keyword in all_keywords:
                        keyword_data = {
                            'keyword': keyword,
                            'source_platform': 'YouTube',
                            'source_url': f"https://www.youtube.com/watch?v={video['id']}",
                            'video_title': snippet.get('title', ''),
                            'video_description': snippet.get('description', '')[:200],
                            'channel_title': snippet.get('channelTitle', ''),
                            'published_at': snippet.get('publishedAt', ''),
                            'view_count': int(statistics.get('viewCount', 0)),
                            'like_count': int(statistics.get('likeCount', 0)),
                            'comment_count': int(statistics.get('commentCount', 0)),
                            'collected_date': datetime.now().isoformat(),
                            'search_query': query
                        }
                        videos_data.append(keyword_data)
            
            time.sleep(0.1)  # API限制
            
        except Exception as e:
            logging.error(f"搜索YouTube视频失败 (查询: {query}): {str(e)}")
        
        return videos_data
    
    def get_trending_videos(self, region_code: str = 'US', max_results: int = 50) -> List[Dict[str, Any]]:
        """获取趋势视频"""
        if not self.youtube:
            return []
        
        trending_data = []
        
        try:
            # 获取趋势视频
            trending_response = self.youtube.videos().list(
                part='snippet,statistics',
                chart='mostPopular',
                regionCode=region_code,
                maxResults=max_results,
                videoCategoryId='20'  # Gaming category
            ).execute()
            
            for video in trending_response['items']:
                snippet = video['snippet']
                statistics = video.get('statistics', {})
                
                # 从标题和描述提取关键词
                title_keywords = self.extract_keywords_from_text(snippet.get('title', ''))
                desc_keywords = self.extract_keywords_from_text(snippet.get('description', ''))
                
                all_keywords = list(set(title_keywords + desc_keywords))
                
                for keyword in all_keywords:
                    keyword_data = {
                        'keyword': keyword,
                        'source_platform': 'YouTube',
                        'source_url': f"https://www.youtube.com/watch?v={video['id']}",
                        'video_title': snippet.get('title', ''),
                        'video_description': snippet.get('description', '')[:200],
                        'channel_title': snippet.get('channelTitle', ''),
                        'published_at': snippet.get('publishedAt', ''),
                        'view_count': int(statistics.get('viewCount', 0)),
                        'like_count': int(statistics.get('likeCount', 0)),
                        'comment_count': int(statistics.get('commentCount', 0)),
                        'collected_date': datetime.now().isoformat(),
                        'is_trending': True,
                        'region_code': region_code
                    }
                    trending_data.append(keyword_data)
            
        except Exception as e:
            logging.error(f"获取YouTube趋势视频失败: {str(e)}")
        
        return trending_data
    
    def collect_keywords(self) -> List[Dict[str, Any]]:
        """收集YouTube关键词"""
        if not self.youtube:
            logging.error("YouTube API未初始化")
            return []
        
        all_keywords = []
        max_videos = self.monitoring_config.get('max_youtube_videos', 50)
        videos_per_query = max_videos // len(self.search_queries)
        
        logging.info(f"开始从YouTube收集关键词，使用 {len(self.search_queries)} 个搜索词")
        
        # 从搜索结果收集
        for query in self.search_queries:
            logging.info(f"正在搜索: {query}")
            keywords = self.search_videos(query, videos_per_query)
            all_keywords.extend(keywords)
            time.sleep(1)  # API限制
        
        # 从趋势视频收集
        logging.info("正在收集趋势视频")
        trending_keywords = self.get_trending_videos()
        all_keywords.extend(trending_keywords)
        
        # 去重处理
        unique_keywords = {}
        for keyword_data in all_keywords:
            keyword = keyword_data['keyword']
            if keyword not in unique_keywords:
                unique_keywords[keyword] = keyword_data
            else:
                # 如果关键词已存在，保留观看量更高的视频
                if keyword_data['view_count'] > unique_keywords[keyword]['view_count']:
                    unique_keywords[keyword] = keyword_data
        
        result = list(unique_keywords.values())
        logging.info(f"YouTube收集完成，共获得 {len(result)} 个唯一关键词")
        
        return result
    
    def get_channel_videos(self, channel_id: str, max_results: int = 25) -> List[Dict[str, Any]]:
        """获取特定频道的视频"""
        if not self.youtube:
            return []
        
        channel_data = []
        
        try:
            # 获取频道的上传播放列表ID
            channel_response = self.youtube.channels().list(
                part='contentDetails',
                id=channel_id
            ).execute()
            
            if not channel_response['items']:
                return []
            
            uploads_playlist_id = channel_response['items'][0]['contentDetails']['relatedPlaylists']['uploads']
            
            # 获取播放列表中的视频
            playlist_response = self.youtube.playlistItems().list(
                part='snippet',
                playlistId=uploads_playlist_id,
                maxResults=max_results
            ).execute()
            
            video_ids = []
            for item in playlist_response['items']:
                video_ids.append(item['snippet']['resourceId']['videoId'])
            
            # 获取视频详细信息
            if video_ids:
                videos_response = self.youtube.videos().list(
                    part='snippet,statistics',
                    id=','.join(video_ids)
                ).execute()
                
                for video in videos_response['items']:
                    snippet = video['snippet']
                    statistics = video.get('statistics', {})
                    
                    # 从标题和描述提取关键词
                    title_keywords = self.extract_keywords_from_text(snippet.get('title', ''))
                    desc_keywords = self.extract_keywords_from_text(snippet.get('description', ''))
                    
                    all_keywords = list(set(title_keywords + desc_keywords))
                    
                    for keyword in all_keywords:
                        keyword_data = {
                            'keyword': keyword,
                            'source_platform': 'YouTube',
                            'source_url': f"https://www.youtube.com/watch?v={video['id']}",
                            'video_title': snippet.get('title', ''),
                            'channel_title': snippet.get('channelTitle', ''),
                            'published_at': snippet.get('publishedAt', ''),
                            'view_count': int(statistics.get('viewCount', 0)),
                            'like_count': int(statistics.get('likeCount', 0)),
                            'comment_count': int(statistics.get('commentCount', 0)),
                            'collected_date': datetime.now().isoformat(),
                            'source_channel_id': channel_id
                        }
                        channel_data.append(keyword_data)
            
        except Exception as e:
            logging.error(f"获取频道视频失败 (频道ID: {channel_id}): {str(e)}")
        
        return channel_data
