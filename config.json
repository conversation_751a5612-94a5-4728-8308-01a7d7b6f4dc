﻿{
  "reddit": {
    "client_id": "YOUR_REDDIT_CLIENT_ID",
    "client_secret": "YOUR_REDDIT_CLIENT_SECRET",
    "user_agent": "H5GameKeywordHunter/1.0"
  },
  "youtube": {
    "api_key": "YOUR_YOUTUBE_API_KEY"
  },
  "notion": {
    "token": "YOUR_NOTION_TOKEN",
    "database_id": "YOUR_NOTION_DATABASE_ID"
  },
  "google_search": {
    "api_key": "YOUR_GOOGLE_SEARCH_API_KEY",
    "search_engine_id": "YOUR_CUSTOM_SEARCH_ENGINE_ID"
  },
  "monitoring": {
    "reddit_subreddits": [
      "WebGames",
      "browsergames",
      "InternetIsBeautiful",
      "gaming",
      "FlashGames",
      "GameIdeas",
      "incremental_games",
      "ioGames"
    ],
    "keywords_to_track": [
      "addictive game",
      "browser game",
      "html5 game",
      "web game",
      "simple game",
      "like wordle",
      "viral game",
      "trending game",
      "io game",
      "casual game",
      "puzzle game",
      "idle game"
    ],
    "max_posts_per_subreddit": 20,
    "max_youtube_videos": 50,
    "collection_interval_hours": 6,
    "enable_auto_collection": true
  },
  "scheduling": {
    "run_time": "09:00",
    "timezone": "Asia/Shanghai",
    "enable_scheduler": false
  },
  "analysis": {
    "enable_trends_analysis": true,
    "enable_seo_analysis": true,
    "trends_timeframes": ["now 7-d", "today 1-m", "today 3-m"],
    "min_trend_score": 10,
    "max_kgr_value": 0.5
  },
  "filters": {
    "min_keyword_length": 3,
    "max_keyword_length": 50,
    "exclude_patterns": [
      "reddit",
      "upvote",
      "comment",
      "subscribe",
      "youtube",
      "tiktok"
    ],
    "required_patterns": [
      "game",
      "play",
      "gaming"
    ]
  }
}
