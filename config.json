{"reddit": {"client_id": "yVxuOa1t520smIjUVTsFiA", "client_secret": "4DOdk4bbA9kZd1BB4_9hWBexz1LyNQ", "user_agent": "H5GameKeywordHunter/1.0"}, "youtube": {"api_key": "AIzaSyDmBoAgZJtaSIyq8jaWnnNHviiCigykHUs"}, "notion": {"token": "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", "database_id": "20abc34f1d4b8090b13eef18797a0acd"}, "google_search": {"api_key": "AIzaSyAbpZaQeT0AmAYJ8fWkQ7ACZZXS01bLf4Q", "search_engine_id": "e35729355526e4ea2", "daily_quota": 100, "enable_safe_search": true}, "monitoring": {"reddit_subreddits": ["WebGames", "browsergames", "InternetIsBeautiful", "gaming", "FlashGames", "GameIdeas", "incremental_games", "ioGames"], "keywords_to_track": ["addictive game", "browser game", "html5 game", "web game", "simple game", "like wordle", "viral game", "trending game", "io game", "casual game", "puzzle game", "idle game"], "max_posts_per_subreddit": 20, "max_youtube_videos": 50, "collection_interval_hours": 6, "enable_auto_collection": true}, "scheduling": {"run_time": "09:00", "timezone": "Asia/Shanghai", "enable_scheduler": false}, "analysis": {"enable_trends_analysis": true, "enable_seo_analysis": true, "trends_timeframes": ["now 7-d", "today 1-m", "today 3-m"], "min_trend_score": 10, "max_kgr_value": 0.5}, "filters": {"min_keyword_length": 3, "max_keyword_length": 50, "exclude_patterns": ["reddit", "upvote", "comment", "subscribe", "youtube", "tiktok"], "required_patterns": ["game", "play", "gaming"]}}