#!/usr/bin/env python3
"""
网络连接修复脚本
"""

import os
import sys
import requests
from urllib.parse import urlparse

def test_google_connectivity():
    """测试Google服务连通性"""
    print("🔍 测试Google服务连通性...")
    
    test_urls = [
        "https://www.googleapis.com",
        "https://youtube.googleapis.com",
        "https://customsearch.googleapis.com"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10)
            print(f"✅ {url} - 连接成功 (状态码: {response.status_code})")
        except requests.exceptions.Timeout:
            print(f"❌ {url} - 连接超时")
        except requests.exceptions.ConnectionError:
            print(f"❌ {url} - 连接错误")
        except Exception as e:
            print(f"❌ {url} - 其他错误: {e}")

def check_proxy_settings():
    """检查代理设置"""
    print("\n🔍 检查代理设置...")
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: 未设置")

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 解决方案建议:")
    print("="*50)
    
    print("\n1. 🌐 检查网络连接")
    print("   - 确保可以访问 https://www.google.com")
    print("   - 尝试在浏览器中访问 https://console.cloud.google.com")
    
    print("\n2. 🔧 配置代理 (如果需要)")
    print("   在命令行中设置代理环境变量:")
    print("   set HTTP_PROXY=http://proxy.company.com:8080")
    print("   set HTTPS_PROXY=http://proxy.company.com:8080")
    
    print("\n3. 🛡️ 检查防火墙设置")
    print("   - 允许Python访问互联网")
    print("   - 允许访问 *.googleapis.com 域名")
    
    print("\n4. 🔄 使用备用方案")
    print("   - 暂时禁用YouTube和Google Search功能")
    print("   - 仅使用Reddit和Notion功能")
    
    print("\n5. 📱 移动热点测试")
    print("   - 尝试使用手机热点连接")
    print("   - 排除网络环境问题")

def create_proxy_config():
    """创建代理配置文件"""
    print("\n🔧 创建代理配置...")
    
    proxy_host = input("请输入代理服务器地址 (留空跳过): ").strip()
    if not proxy_host:
        print("跳过代理配置")
        return
    
    proxy_port = input("请输入代理端口 (默认8080): ").strip() or "8080"
    
    proxy_url = f"http://{proxy_host}:{proxy_port}"
    
    # 创建代理配置文件
    proxy_config = f"""# 代理配置
# 在运行应用前执行以下命令:

set HTTP_PROXY={proxy_url}
set HTTPS_PROXY={proxy_url}

# 或者在Python代码中设置:
import os
os.environ['HTTP_PROXY'] = '{proxy_url}'
os.environ['HTTPS_PROXY'] = '{proxy_url}'
"""
    
    with open('proxy_config.txt', 'w', encoding='utf-8') as f:
        f.write(proxy_config)
    
    print(f"✅ 代理配置已保存到 proxy_config.txt")
    print(f"代理地址: {proxy_url}")

def main():
    """主函数"""
    print("🔧 H5 Game Keyword Hunter - 网络连接修复工具")
    print("="*60)
    
    # 测试连通性
    test_google_connectivity()
    
    # 检查代理
    check_proxy_settings()
    
    # 建议解决方案
    suggest_solutions()
    
    # 询问是否配置代理
    print("\n" + "="*60)
    response = input("是否需要配置代理? (y/N): ").strip().lower()
    if response == 'y':
        create_proxy_config()
    
    print("\n🎯 下一步:")
    print("1. 根据上述建议解决网络问题")
    print("2. 重新运行: python test_apis.py")
    print("3. 如果仍有问题，可以先使用Reddit+Notion功能")

if __name__ == "__main__":
    main()
