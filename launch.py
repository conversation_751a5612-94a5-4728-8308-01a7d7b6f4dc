#!/usr/bin/env python3
"""
Simple launcher for H5 Game Keyword Hunter
"""

import sys
import subprocess
from pathlib import Path

def main():
    print("H5 Game Keyword Hunter - Launcher")
    print("=" * 40)
    
    # Check if main.py exists
    if not Path("main.py").exists():
        print("Error: main.py not found")
        print("Please make sure you are in the correct directory")
        input("Press Enter to exit...")
        return
    
    # Check Python version
    if sys.version_info < (3, 8):
        print(f"Error: Python 3.8+ required, you have {sys.version}")
        input("Press Enter to exit...")
        return
    
    print("Starting application...")
    print("Browser will open at: http://localhost:8501")
    print("Press Ctrl+C to stop")
    print("-" * 40)
    
    try:
        # Start streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "main.py",
            "--server.port", "8501"
        ])
    except KeyboardInterrupt:
        print("\nApplication stopped")
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
