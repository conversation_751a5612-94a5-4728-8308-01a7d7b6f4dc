import streamlit as st

# 页面配置 - 必须是第一个 Streamlit 命令
st.set_page_config(
    page_title="H5游戏关键词猎手",
    page_icon="🎮",
    layout="wide",
    initial_sidebar_state="expanded"
)

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import asyncio
import threading
import time
from typing import List, Dict, Any
import json

# 导入自定义模块
from config import Config

# 尝试导入可选模块
try:
    from collectors.reddit_collector import RedditCollector
    REDDIT_AVAILABLE = True
    REDDIT_ERROR = None
except ImportError as e:
    REDDIT_AVAILABLE = False
    REDDIT_ERROR = "Reddit收集器不可用：缺少praw包"

try:
    from collectors.youtube_collector import YouTubeCollector
    YOUTUBE_AVAILABLE = True
    YOUTUBE_ERROR = None
except ImportError as e:
    YOUTUBE_AVAILABLE = False
    YOUTUBE_ERROR = "YouTube收集器不可用：缺少google-api-python-client包"

try:
    from analyzers.trends_analyzer import TrendsAnalyzer
    TRENDS_AVAILABLE = True
    TRENDS_ERROR = None
except ImportError as e:
    TRENDS_AVAILABLE = False
    TRENDS_ERROR = "趋势分析器不可用：缺少pytrends包"

try:
    from analyzers.seo_analyzer import SEOAnalyzer
    SEO_AVAILABLE = True
    SEO_ERROR = None
except ImportError as e:
    SEO_AVAILABLE = False
    SEO_ERROR = "SEO分析器不可用：缺少相关包"

try:
    from notion_sync import NotionSync
    NOTION_AVAILABLE = True
    NOTION_ERROR = None
except ImportError as e:
    NOTION_AVAILABLE = False
    NOTION_ERROR = "Notion同步不可用：缺少notion-client包"

# 自定义CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .status-running {
        color: #28a745;
    }
    .status-stopped {
        color: #dc3545;
    }
    .keyword-card {
        background-color: #ffffff;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #e0e0e0;
        margin-bottom: 0.5rem;
    }
</style>
""", unsafe_allow_html=True)

class H5GameKeywordHunter:
    def __init__(self):
        self.config = Config()
        self.reddit_collector = None
        self.youtube_collector = None
        self.trends_analyzer = None
        self.seo_analyzer = None
        self.notion_sync = None
        self.is_running = False
        self.collected_keywords = []
        
    def initialize_components(self):
        """初始化所有组件"""
        success_count = 0
        total_count = 0

        try:
            # 初始化Reddit收集器
            if REDDIT_AVAILABLE:
                total_count += 1
                try:
                    self.reddit_collector = RedditCollector(self.config)
                    if hasattr(self.reddit_collector, 'reddit') and self.reddit_collector.reddit:
                        st.success("✅ Reddit API连接成功")
                        success_count += 1
                    else:
                        st.warning("⚠️ Reddit API连接失败，将跳过Reddit数据收集")
                except Exception as e:
                    st.warning(f"⚠️ Reddit初始化失败: {str(e)}")

            # 初始化YouTube收集器
            if YOUTUBE_AVAILABLE:
                total_count += 1
                try:
                    self.youtube_collector = YouTubeCollector(self.config)
                    if hasattr(self.youtube_collector, 'youtube') and self.youtube_collector.youtube:
                        st.success("✅ YouTube API连接成功")
                        success_count += 1
                    else:
                        st.warning("⚠️ YouTube API连接失败，将跳过YouTube数据收集")
                except Exception as e:
                    st.warning(f"⚠️ YouTube初始化失败: {str(e)}")

            # 初始化Notion同步
            if NOTION_AVAILABLE:
                total_count += 1
                try:
                    self.notion_sync = NotionSync(self.config)
                    if hasattr(self.notion_sync, 'notion') and self.notion_sync.notion:
                        st.success("✅ Notion API连接成功")
                        success_count += 1
                    else:
                        st.warning("⚠️ Notion API连接失败，将无法同步数据到Notion")
                except Exception as e:
                    st.warning(f"⚠️ Notion初始化失败: {str(e)}")

            # 初始化分析器
            if TRENDS_AVAILABLE:
                total_count += 1
                try:
                    self.trends_analyzer = TrendsAnalyzer()
                    st.success("✅ 趋势分析器初始化成功")
                    success_count += 1
                except Exception as e:
                    st.warning(f"⚠️ 趋势分析器初始化失败: {str(e)}")

            if SEO_AVAILABLE:
                total_count += 1
                try:
                    self.seo_analyzer = SEOAnalyzer(self.config)
                    if hasattr(self.seo_analyzer, 'search_service') and self.seo_analyzer.search_service:
                        st.success("✅ SEO分析器初始化成功")
                        success_count += 1
                    else:
                        st.warning("⚠️ Google Search API连接失败，SEO分析功能受限")
                        success_count += 1  # 仍然算作成功，只是功能受限
                except Exception as e:
                    st.warning(f"⚠️ SEO分析器初始化失败: {str(e)}")

            # 显示初始化结果
            if success_count > 0:
                st.info(f"📊 组件初始化完成: {success_count}/{total_count} 个组件可用")
                return True
            else:
                st.error("❌ 所有组件初始化失败，无法启动应用")
                return False

        except Exception as e:
            st.error(f"组件初始化失败: {str(e)}")
            return False
    
    def collect_keywords_from_all_sources(self) -> List[Dict[str, Any]]:
        """从所有来源收集关键词"""
        all_keywords = []
        
        # Reddit关键词收集
        if self.reddit_collector:
            reddit_keywords = self.reddit_collector.collect_keywords()
            for keyword in reddit_keywords:
                keyword['source'] = 'Reddit'
                all_keywords.append(keyword)
        
        # YouTube关键词收集
        if self.youtube_collector:
            youtube_keywords = self.youtube_collector.collect_keywords()
            for keyword in youtube_keywords:
                keyword['source'] = 'YouTube'
                all_keywords.append(keyword)
        
        return all_keywords
    
    def analyze_keywords(self, keywords: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分析关键词趋势和SEO数据"""
        analyzed_keywords = []
        
        for keyword_data in keywords:
            keyword = keyword_data['keyword']
            
            # 趋势分析
            if self.trends_analyzer:
                trends_data = self.trends_analyzer.get_trends_data(keyword)
                keyword_data.update(trends_data)
            
            # SEO分析
            if self.seo_analyzer:
                seo_data = self.seo_analyzer.analyze_keyword(keyword)
                keyword_data.update(seo_data)
            
            analyzed_keywords.append(keyword_data)
        
        return analyzed_keywords
    
    def sync_to_notion(self, keywords: List[Dict[str, Any]]):
        """同步到Notion"""
        if self.notion_sync:
            return self.notion_sync.batch_create_pages(keywords)
        return False

def main():
    # 显示模块导入警告
    if not REDDIT_AVAILABLE and REDDIT_ERROR:
        st.warning(REDDIT_ERROR)
    if not YOUTUBE_AVAILABLE and YOUTUBE_ERROR:
        st.warning(YOUTUBE_ERROR)
    if not TRENDS_AVAILABLE and TRENDS_ERROR:
        st.warning(TRENDS_ERROR)
    if not SEO_AVAILABLE and SEO_ERROR:
        st.warning(SEO_ERROR)
    if not NOTION_AVAILABLE and NOTION_ERROR:
        st.warning(NOTION_ERROR)

    # 主标题
    st.markdown('<h1 class="main-header">🎮 H5游戏关键词猎手</h1>', unsafe_allow_html=True)

    # 初始化应用
    if 'app' not in st.session_state:
        st.session_state.app = H5GameKeywordHunter()

    app = st.session_state.app
    
    # 侧边栏配置
    with st.sidebar:
        st.header("⚙️ 配置管理")
        
        # 配置验证
        if st.button("验证配置"):
            if app.config.validate_config():
                st.success("✅ 配置验证通过")
                if app.initialize_components():
                    st.success("✅ 组件初始化成功")
            else:
                st.error("❌ 配置验证失败")
        
        st.divider()
        
        # 运行控制
        st.header("🚀 运行控制")
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("开始收集", type="primary"):
                if app.initialize_components():
                    app.is_running = True
                    st.success("开始收集关键词...")
                else:
                    st.error("初始化失败，无法开始收集")
        
        with col2:
            if st.button("停止收集"):
                app.is_running = False
                st.info("已停止收集")
        
        # 运行状态
        status_color = "status-running" if app.is_running else "status-stopped"
        status_text = "运行中" if app.is_running else "已停止"
        st.markdown(f'<p class="{status_color}">状态: {status_text}</p>', unsafe_allow_html=True)
        
        st.divider()
        
        # 手动操作
        st.header("🔧 手动操作")
        
        if st.button("立即收集一次"):
            if app.initialize_components():
                with st.spinner("正在收集关键词..."):
                    keywords = app.collect_keywords_from_all_sources()
                    if keywords:
                        analyzed_keywords = app.analyze_keywords(keywords)
                        app.collected_keywords = analyzed_keywords
                        st.success(f"收集到 {len(analyzed_keywords)} 个关键词")
                    else:
                        st.warning("未收集到关键词")
        
        if st.button("同步到Notion") and app.collected_keywords:
            with st.spinner("正在同步到Notion..."):
                if app.sync_to_notion(app.collected_keywords):
                    st.success("同步成功!")
                else:
                    st.error("同步失败")
    
    # 主内容区域
    tab1, tab2, tab3, tab4 = st.tabs(["📊 仪表板", "🔍 关键词列表", "📈 趋势分析", "⚙️ 设置"])
    
    with tab1:
        st.header("📊 数据仪表板")
        
        # 统计指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("今日收集", len(app.collected_keywords), "↗️ +12")
        
        with col2:
            high_trend_count = len([k for k in app.collected_keywords if k.get('trend_score_7d', 0) > 50])
            st.metric("高趋势关键词", high_trend_count, "↗️ +3")
        
        with col3:
            low_kgr_count = len([k for k in app.collected_keywords if k.get('kgr_value', 1) < 0.25])
            st.metric("低竞争关键词", low_kgr_count, "↗️ +5")
        
        with col4:
            st.metric("Notion同步", "已同步" if app.collected_keywords else "待同步", "✅")
        
        # 数据可视化
        if app.collected_keywords:
            st.subheader("📈 关键词来源分布")
            source_data = pd.DataFrame(app.collected_keywords)
            if 'source' in source_data.columns:
                source_counts = source_data['source'].value_counts()
                fig_pie = px.pie(values=source_counts.values, names=source_counts.index, 
                               title="关键词来源分布")
                st.plotly_chart(fig_pie, use_container_width=True)
            
            st.subheader("📊 趋势评分分布")
            if 'trend_score_7d' in source_data.columns:
                fig_hist = px.histogram(source_data, x='trend_score_7d', 
                                      title="7天趋势评分分布", nbins=20)
                st.plotly_chart(fig_hist, use_container_width=True)
    
    with tab2:
        st.header("🔍 关键词列表")
        
        if app.collected_keywords:
            # 过滤选项
            col1, col2, col3 = st.columns(3)
            
            with col1:
                source_filter = st.selectbox("来源过滤", 
                    ["全部"] + list(set([k.get('source', '') for k in app.collected_keywords])))
            
            with col2:
                min_trend = st.slider("最小趋势评分", 0, 100, 0)
            
            with col3:
                max_kgr = st.slider("最大KGR值", 0.0, 2.0, 2.0, 0.1)
            
            # 过滤数据
            filtered_keywords = app.collected_keywords
            if source_filter != "全部":
                filtered_keywords = [k for k in filtered_keywords if k.get('source') == source_filter]
            filtered_keywords = [k for k in filtered_keywords if k.get('trend_score_7d', 0) >= min_trend]
            filtered_keywords = [k for k in filtered_keywords if k.get('kgr_value', 2.0) <= max_kgr]
            
            # 显示关键词卡片
            for keyword_data in filtered_keywords[:20]:  # 限制显示数量
                with st.container():
                    col1, col2, col3, col4 = st.columns([3, 1, 1, 1])
                    
                    with col1:
                        st.write(f"**{keyword_data.get('keyword', 'N/A')}**")
                        st.write(f"来源: {keyword_data.get('source', 'N/A')}")
                    
                    with col2:
                        trend_score = keyword_data.get('trend_score_7d', 0)
                        st.metric("7天趋势", f"{trend_score}")
                    
                    with col3:
                        kgr_value = keyword_data.get('kgr_value', 0)
                        st.metric("KGR值", f"{kgr_value:.2f}")
                    
                    with col4:
                        if st.button("查看详情", key=f"detail_{keyword_data.get('keyword', '')}"):
                            st.session_state.selected_keyword = keyword_data
                
                st.divider()
        else:
            st.info("暂无关键词数据，请先进行收集")
    
    with tab3:
        st.header("📈 趋势分析")
        
        if app.collected_keywords:
            # 趋势图表
            df = pd.DataFrame(app.collected_keywords)
            
            if 'trend_score_7d' in df.columns and 'trend_score_30d' in df.columns:
                fig_scatter = px.scatter(df, x='trend_score_7d', y='trend_score_30d',
                                       hover_data=['keyword', 'source'],
                                       title="7天 vs 30天趋势对比")
                st.plotly_chart(fig_scatter, use_container_width=True)
            
            # 热门关键词排行
            st.subheader("🔥 热门关键词排行")
            if 'trend_score_7d' in df.columns:
                top_keywords = df.nlargest(10, 'trend_score_7d')[['keyword', 'trend_score_7d', 'source']]
                st.dataframe(top_keywords, use_container_width=True)
        else:
            st.info("暂无趋势数据")
    
    with tab4:
        st.header("⚙️ 系统设置")
        
        # 配置编辑
        st.subheader("📝 配置编辑")
        
        config_section = st.selectbox("选择配置节", 
            ["reddit", "youtube", "notion", "google_search", "monitoring"])
        
        if config_section:
            current_config = getattr(app.config, f"get_{config_section}_config")()
            
            st.json(current_config)
            
            st.info("要修改配置，请编辑 config.json 文件后重启应用")
        
        # 系统信息
        st.subheader("ℹ️ 系统信息")
        st.write(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        st.write(f"配置文件: config.json")
        st.write(f"数据收集状态: {'运行中' if app.is_running else '已停止'}")

if __name__ == "__main__":
    main()
