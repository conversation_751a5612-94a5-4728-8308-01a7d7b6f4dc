#!/usr/bin/env python3
"""
H5游戏关键词猎手 - 运行状态监控器
"""

import time
import requests
import json
import os
from datetime import datetime
import subprocess
import sys

class AppMonitor:
    def __init__(self):
        self.app_url = "http://localhost:8501"
        self.log_file = "app_monitor.log"
        self.status_file = "app_status.json"
        
    def log_message(self, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        print(log_entry)
        
        # 写入日志文件
        with open(self.log_file, "a", encoding="utf-8") as f:
            f.write(log_entry + "\n")
    
    def check_app_status(self):
        """检查应用运行状态"""
        try:
            response = requests.get(f"{self.app_url}/healthz", timeout=5)
            return True
        except:
            try:
                response = requests.get(self.app_url, timeout=5)
                return response.status_code == 200
            except:
                return False
    
    def get_system_info(self):
        """获取系统信息"""
        try:
            # 检查Python进程
            result = subprocess.run(
                ["tasklist", "/fi", "imagename eq python.exe", "/fo", "csv"],
                capture_output=True, text=True, shell=True
            )
            python_processes = len(result.stdout.split('\n')) - 2  # 减去标题行
            
            return {
                "python_processes": max(0, python_processes),
                "timestamp": datetime.now().isoformat()
            }
        except:
            return {"python_processes": 0, "timestamp": datetime.now().isoformat()}
    
    def save_status(self, status_data):
        """保存状态到文件"""
        try:
            with open(self.status_file, "w", encoding="utf-8") as f:
                json.dump(status_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.log_message(f"保存状态失败: {e}")
    
    def start_monitoring(self):
        """开始监控"""
        self.log_message("🚀 开始监控H5游戏关键词猎手应用")
        self.log_message(f"📍 监控地址: {self.app_url}")
        self.log_message(f"📝 日志文件: {self.log_file}")
        self.log_message(f"📊 状态文件: {self.status_file}")
        self.log_message("=" * 50)
        
        consecutive_failures = 0
        
        try:
            while True:
                # 检查应用状态
                is_running = self.check_app_status()
                system_info = self.get_system_info()
                
                status_data = {
                    "app_running": is_running,
                    "check_time": datetime.now().isoformat(),
                    "app_url": self.app_url,
                    "system_info": system_info
                }
                
                if is_running:
                    if consecutive_failures > 0:
                        self.log_message("✅ 应用恢复正常运行")
                        consecutive_failures = 0
                    else:
                        self.log_message("✅ 应用运行正常")
                    
                    status_data["status"] = "running"
                else:
                    consecutive_failures += 1
                    self.log_message(f"❌ 应用无响应 (连续失败: {consecutive_failures}次)")
                    
                    if consecutive_failures >= 3:
                        self.log_message("⚠️ 应用可能已停止，请检查启动脚本")
                    
                    status_data["status"] = "stopped"
                    status_data["consecutive_failures"] = consecutive_failures
                
                # 保存状态
                self.save_status(status_data)
                
                # 等待下次检查
                time.sleep(30)  # 每30秒检查一次
                
        except KeyboardInterrupt:
            self.log_message("🛑 监控已停止")
        except Exception as e:
            self.log_message(f"❌ 监控出错: {e}")

def main():
    """主函数"""
    print("🎮 H5游戏关键词猎手 - 状态监控器")
    print("=" * 50)
    print("📍 功能说明:")
    print("  - 监控应用运行状态")
    print("  - 记录运行日志")
    print("  - 保存状态信息")
    print("🛑 按 Ctrl+C 停止监控")
    print("=" * 50)
    print()
    
    monitor = AppMonitor()
    monitor.start_monitoring()

if __name__ == "__main__":
    main()
