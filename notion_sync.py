from notion_client import Client
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import time

class NotionSync:
    def __init__(self, config):
        self.config = config
        self.notion_config = config.get_notion_config()
        self.notion = None
        self.database_id = None
        self.setup_notion()
    
    def setup_notion(self):
        """设置Notion客户端"""
        try:
            token = self.notion_config.get('token')
            self.database_id = self.notion_config.get('database_id')
            
            if not token or token == 'YOUR_NOTION_TOKEN':
                logging.error("Notion token未配置")
                return
            
            if not self.database_id or self.database_id == 'YOUR_NOTION_DATABASE_ID':
                logging.error("Notion数据库ID未配置")
                return
            
            self.notion = Client(auth=token)
            
            # 测试连接
            self.notion.databases.retrieve(database_id=self.database_id)
            logging.info("Notion连接成功")
            
        except Exception as e:
            logging.error(f"Notion连接失败: {str(e)}")
            self.notion = None
    
    def create_page(self, keyword_data: Dict[str, Any]) -> bool:
        """创建单个页面"""
        if not self.notion or not self.database_id:
            return False
        
        try:
            # 构建页面属性
            properties = self._build_page_properties(keyword_data)
            
            # 创建页面
            response = self.notion.pages.create(
                parent={"database_id": self.database_id},
                properties=properties
            )
            
            logging.info(f"成功创建页面: {keyword_data.get('keyword', 'Unknown')}")
            return True
            
        except Exception as e:
            logging.error(f"创建页面失败: {str(e)}")
            return False
    
    def _build_page_properties(self, keyword_data: Dict[str, Any]) -> Dict[str, Any]:
        """构建页面属性"""
        properties = {}
        
        # 关键词 (Title)
        if 'keyword' in keyword_data:
            properties['关键词'] = {
                "title": [
                    {
                        "text": {
                            "content": keyword_data['keyword']
                        }
                    }
                ]
            }
        
        # 来源平台 (Select)
        if 'source_platform' in keyword_data:
            properties['来源平台'] = {
                "select": {
                    "name": keyword_data['source_platform']
                }
            }
        
        # 采集日期 (Date)
        if 'collected_date' in keyword_data:
            try:
                # 确保日期格式正确
                date_str = keyword_data['collected_date']
                if 'T' in date_str:
                    date_str = date_str.split('T')[0]
                
                properties['采集日期'] = {
                    "date": {
                        "start": date_str
                    }
                }
            except:
                properties['采集日期'] = {
                    "date": {
                        "start": datetime.now().strftime('%Y-%m-%d')
                    }
                }
        
        # 7天趋势评分 (Number)
        if 'trend_score_7d' in keyword_data:
            properties['7天趋势评分'] = {
                "number": float(keyword_data['trend_score_7d'])
            }
        
        # 30天趋势评分 (Number)
        if 'trend_score_30d' in keyword_data:
            properties['30天趋势评分'] = {
                "number": float(keyword_data['trend_score_30d'])
            }
        
        # Google Trends 链接 (URL)
        if 'google_trends_url' in keyword_data and keyword_data['google_trends_url']:
            properties['Google Trends 链接'] = {
                "url": keyword_data['google_trends_url']
            }
        
        # 是否已被注册域名 (Checkbox)
        if 'domain_available' in keyword_data:
            properties['是否已被注册域名'] = {
                "checkbox": not keyword_data['domain_available']  # 注意逻辑反转
            }
        
        # SERP 是否有图片 (Checkbox)
        if 'serp_has_images' in keyword_data:
            properties['SERP 是否有图片'] = {
                "checkbox": keyword_data['serp_has_images']
            }
        
        # SERP 是否有大站 (Checkbox)
        if 'serp_has_major_sites' in keyword_data:
            properties['SERP 是否有大站'] = {
                "checkbox": keyword_data['serp_has_major_sites']
            }
        
        # allintitle 数值 (Number)
        if 'allintitle_count' in keyword_data:
            properties['allintitle 数值'] = {
                "number": int(keyword_data['allintitle_count'])
            }
        
        # KGR 值 (Number)
        if 'kgr_value' in keyword_data:
            properties['KGR 值'] = {
                "number": float(keyword_data['kgr_value'])
            }
        
        # 备注 (Rich Text)
        notes = []
        
        # 添加来源信息到备注
        if 'source_url' in keyword_data:
            notes.append(f"来源链接: {keyword_data['source_url']}")
        
        if 'post_title' in keyword_data:
            notes.append(f"帖子标题: {keyword_data['post_title']}")
        
        if 'video_title' in keyword_data:
            notes.append(f"视频标题: {keyword_data['video_title']}")
        
        if 'post_score' in keyword_data:
            notes.append(f"帖子评分: {keyword_data['post_score']}")
        
        if 'view_count' in keyword_data:
            notes.append(f"观看次数: {keyword_data['view_count']}")
        
        if 'related_queries' in keyword_data and keyword_data['related_queries']:
            related = ', '.join(keyword_data['related_queries'][:3])
            notes.append(f"相关查询: {related}")
        
        if notes:
            properties['备注'] = {
                "rich_text": [
                    {
                        "text": {
                            "content": '\n'.join(notes)
                        }
                    }
                ]
            }
        
        return properties
    
    def batch_create_pages(self, keywords_data: List[Dict[str, Any]]) -> bool:
        """批量创建页面"""
        if not self.notion or not self.database_id:
            logging.error("Notion未初始化")
            return False
        
        success_count = 0
        total_count = len(keywords_data)
        
        logging.info(f"开始批量创建 {total_count} 个页面")
        
        for i, keyword_data in enumerate(keywords_data):
            try:
                # 检查是否已存在
                if self._page_exists(keyword_data.get('keyword', '')):
                    logging.info(f"页面已存在，跳过: {keyword_data.get('keyword', '')}")
                    continue
                
                if self.create_page(keyword_data):
                    success_count += 1
                
                # 添加延迟避免API限制
                time.sleep(0.5)
                
                # 每10个页面输出进度
                if (i + 1) % 10 == 0:
                    logging.info(f"进度: {i + 1}/{total_count}")
                
            except Exception as e:
                logging.error(f"批量创建页面失败 (第{i+1}个): {str(e)}")
        
        logging.info(f"批量创建完成: {success_count}/{total_count} 成功")
        return success_count > 0
    
    def _page_exists(self, keyword: str) -> bool:
        """检查页面是否已存在"""
        if not self.notion or not keyword:
            return False
        
        try:
            # 查询数据库
            response = self.notion.databases.query(
                database_id=self.database_id,
                filter={
                    "property": "关键词",
                    "title": {
                        "equals": keyword
                    }
                }
            )
            
            return len(response['results']) > 0
            
        except Exception as e:
            logging.error(f"检查页面存在性失败: {str(e)}")
            return False
    
    def update_page(self, page_id: str, keyword_data: Dict[str, Any]) -> bool:
        """更新页面"""
        if not self.notion:
            return False
        
        try:
            properties = self._build_page_properties(keyword_data)
            
            response = self.notion.pages.update(
                page_id=page_id,
                properties=properties
            )
            
            logging.info(f"成功更新页面: {keyword_data.get('keyword', 'Unknown')}")
            return True
            
        except Exception as e:
            logging.error(f"更新页面失败: {str(e)}")
            return False
    
    def get_existing_keywords(self) -> List[str]:
        """获取已存在的关键词列表"""
        if not self.notion or not self.database_id:
            return []
        
        existing_keywords = []
        
        try:
            # 查询所有页面
            has_more = True
            start_cursor = None
            
            while has_more:
                query_params = {
                    "database_id": self.database_id,
                    "page_size": 100
                }
                
                if start_cursor:
                    query_params["start_cursor"] = start_cursor
                
                response = self.notion.databases.query(**query_params)
                
                for page in response['results']:
                    try:
                        title_property = page['properties'].get('关键词', {})
                        if 'title' in title_property and title_property['title']:
                            keyword = title_property['title'][0]['text']['content']
                            existing_keywords.append(keyword)
                    except:
                        continue
                
                has_more = response['has_more']
                start_cursor = response.get('next_cursor')
            
            logging.info(f"获取到 {len(existing_keywords)} 个已存在的关键词")
            
        except Exception as e:
            logging.error(f"获取已存在关键词失败: {str(e)}")
        
        return existing_keywords
    
    def delete_page(self, page_id: str) -> bool:
        """删除页面"""
        if not self.notion:
            return False
        
        try:
            # Notion API不支持直接删除，只能归档
            response = self.notion.pages.update(
                page_id=page_id,
                archived=True
            )
            
            logging.info(f"成功归档页面: {page_id}")
            return True
            
        except Exception as e:
            logging.error(f"归档页面失败: {str(e)}")
            return False
    
    def get_database_schema(self) -> Dict[str, Any]:
        """获取数据库结构"""
        if not self.notion or not self.database_id:
            return {}
        
        try:
            response = self.notion.databases.retrieve(database_id=self.database_id)
            return response.get('properties', {})
            
        except Exception as e:
            logging.error(f"获取数据库结构失败: {str(e)}")
            return {}
    
    def test_connection(self) -> bool:
        """测试连接"""
        if not self.notion or not self.database_id:
            return False
        
        try:
            self.notion.databases.retrieve(database_id=self.database_id)
            return True
        except Exception as e:
            logging.error(f"Notion连接测试失败: {str(e)}")
            return False
