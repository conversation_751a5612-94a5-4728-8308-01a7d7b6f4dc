#!/usr/bin/env python3
"""
配置向导 - 帮助用户设置API密钥
"""

import json
import os
from pathlib import Path

def print_header():
    """打印标题"""
    print("\n" + "="*50)
    print("🎮 H5游戏关键词猎手 - 配置向导")
    print("="*50)
    print("这个向导将帮助您配置API密钥")
    print()

def print_api_guide(api_name, steps):
    """打印API获取指南"""
    print(f"\n📋 {api_name} API 获取指南:")
    print("-" * 30)
    for i, step in enumerate(steps, 1):
        print(f"{i}. {step}")
    print()

def get_reddit_config():
    """获取Reddit配置"""
    print_api_guide("Reddit", [
        "访问 https://www.reddit.com/prefs/apps",
        "点击 'Create App' 或 'Create Another App'",
        "选择 'script' 类型",
        "填写应用名称和描述",
        "获取 client_id 和 client_secret"
    ])
    
    client_id = input("请输入Reddit Client ID (留空跳过): ").strip()
    client_secret = input("请输入Reddit Client Secret (留空跳过): ").strip()
    
    if client_id and client_secret:
        return {
            "client_id": client_id,
            "client_secret": client_secret,
            "user_agent": "H5GameKeywordHunter/1.0"
        }
    return None

def get_youtube_config():
    """获取YouTube配置"""
    print_api_guide("YouTube", [
        "访问 https://console.cloud.google.com/",
        "创建新项目或选择现有项目",
        "启用 YouTube Data API v3",
        "创建API密钥",
        "复制API密钥"
    ])
    
    api_key = input("请输入YouTube API Key (留空跳过): ").strip()
    
    if api_key:
        return {"api_key": api_key}
    return None

def get_notion_config():
    """获取Notion配置"""
    print_api_guide("Notion", [
        "访问 https://www.notion.so/my-integrations",
        "创建新的集成",
        "获取 Internal Integration Token",
        "在Notion中创建数据库",
        "将数据库共享给集成",
        "复制数据库ID (URL中的32位字符)"
    ])
    
    token = input("请输入Notion Integration Token (留空跳过): ").strip()
    database_id = input("请输入Notion Database ID (留空跳过): ").strip()
    
    if token and database_id:
        return {
            "token": token,
            "database_id": database_id
        }
    return None

def get_google_search_config():
    """获取Google搜索配置"""
    print_api_guide("Google Custom Search", [
        "访问 https://console.cloud.google.com/",
        "启用 Custom Search API",
        "创建API密钥",
        "访问 https://cse.google.com/",
        "创建自定义搜索引擎",
        "获取搜索引擎ID"
    ])
    
    api_key = input("请输入Google Search API Key (留空跳过): ").strip()
    search_engine_id = input("请输入Custom Search Engine ID (留空跳过): ").strip()
    
    if api_key and search_engine_id:
        return {
            "api_key": api_key,
            "search_engine_id": search_engine_id
        }
    return None

def load_existing_config():
    """加载现有配置"""
    config_file = Path("config.json")
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            pass
    
    # 返回默认配置
    return {
        "reddit": {
            "client_id": "YOUR_REDDIT_CLIENT_ID",
            "client_secret": "YOUR_REDDIT_CLIENT_SECRET",
            "user_agent": "H5GameKeywordHunter/1.0"
        },
        "youtube": {
            "api_key": "YOUR_YOUTUBE_API_KEY"
        },
        "notion": {
            "token": "YOUR_NOTION_TOKEN",
            "database_id": "YOUR_NOTION_DATABASE_ID"
        },
        "google_search": {
            "api_key": "YOUR_GOOGLE_SEARCH_API_KEY",
            "search_engine_id": "YOUR_CUSTOM_SEARCH_ENGINE_ID"
        },
        "monitoring": {
            "reddit_subreddits": [
                "WebGames", "browsergames", "InternetIsBeautiful",
                "gaming", "FlashGames", "GameIdeas", "incremental_games", "ioGames"
            ],
            "keywords_to_track": [
                "addictive game", "browser game", "html5 game", "web game",
                "simple game", "like wordle", "viral game", "trending game",
                "io game", "casual game", "puzzle game", "idle game"
            ],
            "max_posts_per_subreddit": 20,
            "max_youtube_videos": 50,
            "collection_interval_hours": 6,
            "enable_auto_collection": True
        },
        "scheduling": {
            "run_time": "09:00",
            "timezone": "Asia/Shanghai",
            "enable_scheduler": False
        },
        "analysis": {
            "enable_trends_analysis": True,
            "enable_seo_analysis": True,
            "trends_timeframes": ["now 7-d", "today 1-m", "today 3-m"],
            "min_trend_score": 10,
            "max_kgr_value": 0.5
        },
        "filters": {
            "min_keyword_length": 3,
            "max_keyword_length": 50,
            "exclude_patterns": ["reddit", "upvote", "comment", "subscribe", "youtube", "tiktok"],
            "required_patterns": ["game", "play", "gaming"]
        }
    }

def save_config(config):
    """保存配置"""
    try:
        with open("config.json", 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print("✅ 配置已保存到 config.json")
        return True
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False

def main():
    """主函数"""
    print_header()
    
    # 加载现有配置
    config = load_existing_config()
    
    print("请按照提示配置API密钥。如果暂时没有某个API密钥，可以留空跳过。")
    print("您可以稍后手动编辑 config.json 文件来添加缺失的配置。")
    print("\n注意: 至少需要配置 Reddit 和 Notion API 才能正常使用基本功能。")
    
    # 配置各个API
    apis_to_configure = [
        ("Reddit", get_reddit_config, "reddit"),
        ("YouTube", get_youtube_config, "youtube"),
        ("Notion", get_notion_config, "notion"),
        ("Google Search", get_google_search_config, "google_search")
    ]
    
    for api_name, config_func, config_key in apis_to_configure:
        print(f"\n{'='*20} {api_name} 配置 {'='*20}")
        
        # 显示当前配置
        current_config = config.get(config_key, {})
        has_valid_config = False
        
        if config_key == "reddit":
            has_valid_config = (current_config.get("client_id", "").startswith("YOUR_") == False and
                              current_config.get("client_secret", "").startswith("YOUR_") == False)
        elif config_key == "youtube":
            has_valid_config = current_config.get("api_key", "").startswith("YOUR_") == False
        elif config_key == "notion":
            has_valid_config = (current_config.get("token", "").startswith("YOUR_") == False and
                              current_config.get("database_id", "").startswith("YOUR_") == False)
        elif config_key == "google_search":
            has_valid_config = (current_config.get("api_key", "").startswith("YOUR_") == False and
                              current_config.get("search_engine_id", "").startswith("YOUR_") == False)
        
        if has_valid_config:
            print(f"✅ {api_name} 已配置")
            skip = input(f"是否重新配置 {api_name}? (y/N): ").strip().lower()
            if skip != 'y':
                continue
        
        # 获取新配置
        new_config = config_func()
        if new_config:
            config[config_key] = new_config
            print(f"✅ {api_name} 配置完成")
        else:
            print(f"⏭️ 跳过 {api_name} 配置")
    
    # 保存配置
    print(f"\n{'='*50}")
    if save_config(config):
        print("\n🎉 配置向导完成!")
        print("\n下一步:")
        print("1. 运行 'python run_app.py' 启动应用")
        print("2. 或者双击 'start.bat' (Windows)")
        print("3. 在浏览器中访问 http://localhost:8501")
        
        # 检查配置完整性
        missing_configs = []
        if config["reddit"]["client_id"].startswith("YOUR_"):
            missing_configs.append("Reddit")
        if config["notion"]["token"].startswith("YOUR_"):
            missing_configs.append("Notion")
        
        if missing_configs:
            print(f"\n⚠️  警告: {', '.join(missing_configs)} 配置缺失")
            print("应用可能无法正常工作，请稍后完善配置。")
    else:
        print("\n❌ 配置保存失败，请检查文件权限")

if __name__ == "__main__":
    main()
