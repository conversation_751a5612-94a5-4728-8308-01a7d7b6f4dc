#!/usr/bin/env python3
"""
API测试脚本 - 诊断各个API连接问题
"""

import json
import sys
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return None

def test_reddit_api(config):
    """测试Reddit API"""
    print("\n" + "="*50)
    print("🔍 测试 Reddit API")
    print("="*50)
    
    try:
        import praw
        
        reddit_config = config.get('reddit', {})
        client_id = reddit_config.get('client_id')
        client_secret = reddit_config.get('client_secret')
        user_agent = reddit_config.get('user_agent', 'H5GameKeywordHunter/1.0')
        
        print(f"Client ID: {client_id[:10]}..." if client_id else "Client ID: 未配置")
        print(f"Client Secret: {client_secret[:10]}..." if client_secret else "Client Secret: 未配置")
        print(f"User Agent: {user_agent}")
        
        if not client_id or not client_secret:
            print("❌ Reddit API配置不完整")
            return False
        
        # 创建Reddit实例
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent
        )
        
        # 测试连接 - 获取一个subreddit的信息
        test_subreddit = reddit.subreddit('test')
        print(f"✅ Reddit API连接成功")
        print(f"测试subreddit: {test_subreddit.display_name}")
        
        # 测试获取帖子
        posts = list(test_subreddit.hot(limit=1))
        if posts:
            print(f"✅ 成功获取帖子: {posts[0].title[:50]}...")
        
        return True
        
    except ImportError:
        print("❌ praw模块未安装")
        return False
    except Exception as e:
        print(f"❌ Reddit API测试失败: {e}")
        return False

def test_youtube_api(config):
    """测试YouTube API"""
    print("\n" + "="*50)
    print("🔍 测试 YouTube API")
    print("="*50)
    
    try:
        from googleapiclient.discovery import build
        
        youtube_config = config.get('youtube', {})
        api_key = youtube_config.get('api_key')
        
        print(f"API Key: {api_key[:20]}..." if api_key else "API Key: 未配置")
        
        if not api_key or api_key == 'YOUR_YOUTUBE_API_KEY':
            print("❌ YouTube API密钥未配置")
            return False
        
        # 创建YouTube服务
        youtube = build('youtube', 'v3', developerKey=api_key)
        
        # 测试搜索
        search_response = youtube.search().list(
            q='game',
            part='id,snippet',
            maxResults=1,
            type='video'
        ).execute()
        
        if search_response.get('items'):
            video = search_response['items'][0]
            print(f"✅ YouTube API连接成功")
            print(f"测试搜索结果: {video['snippet']['title'][:50]}...")
            return True
        else:
            print("❌ YouTube API返回空结果")
            return False
        
    except ImportError:
        print("❌ google-api-python-client模块未安装")
        return False
    except Exception as e:
        print(f"❌ YouTube API测试失败: {e}")
        return False

def test_notion_api(config):
    """测试Notion API"""
    print("\n" + "="*50)
    print("🔍 测试 Notion API")
    print("="*50)
    
    try:
        from notion_client import Client
        
        notion_config = config.get('notion', {})
        token = notion_config.get('token')
        database_id = notion_config.get('database_id')
        
        print(f"Token: {token[:20]}..." if token else "Token: 未配置")
        print(f"Database ID: {database_id}")
        
        if not token or token == 'YOUR_NOTION_TOKEN':
            print("❌ Notion token未配置")
            return False
        
        if not database_id or database_id == 'YOUR_NOTION_DATABASE_ID':
            print("❌ Notion数据库ID未配置")
            return False
        
        # 创建Notion客户端
        notion = Client(auth=token)
        
        # 测试连接 - 获取数据库信息
        database = notion.databases.retrieve(database_id=database_id)
        print(f"✅ Notion API连接成功")
        print(f"数据库标题: {database.get('title', [{}])[0].get('plain_text', 'Unknown')}")
        
        return True
        
    except ImportError:
        print("❌ notion-client模块未安装")
        return False
    except Exception as e:
        print(f"❌ Notion API测试失败: {e}")
        return False

def test_google_search_api(config):
    """测试Google Search API"""
    print("\n" + "="*50)
    print("🔍 测试 Google Search API")
    print("="*50)
    
    try:
        from googleapiclient.discovery import build
        
        google_config = config.get('google_search', {})
        api_key = google_config.get('api_key')
        search_engine_id = google_config.get('search_engine_id')
        
        print(f"API Key: {api_key[:20]}..." if api_key else "API Key: 未配置")
        print(f"Search Engine ID: {search_engine_id}")
        
        if not api_key or api_key == 'YOUR_GOOGLE_SEARCH_API_KEY':
            print("❌ Google Search API密钥未配置")
            return False
        
        if not search_engine_id or search_engine_id == 'YOUR_CUSTOM_SEARCH_ENGINE_ID':
            print("❌ Google Search Engine ID未配置")
            return False
        
        # 创建搜索服务
        service = build('customsearch', 'v1', developerKey=api_key)
        
        # 测试搜索
        result = service.cse().list(
            q='test game',
            cx=search_engine_id,
            num=1
        ).execute()
        
        if result.get('items'):
            print(f"✅ Google Search API连接成功")
            print(f"测试搜索结果: {result['items'][0]['title'][:50]}...")
            return True
        else:
            print("❌ Google Search API返回空结果")
            return False
        
    except ImportError:
        print("❌ google-api-python-client模块未安装")
        return False
    except Exception as e:
        print(f"❌ Google Search API测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 H5 Game Keyword Hunter - API诊断工具")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 加载配置
    config = load_config()
    if not config:
        sys.exit(1)
    
    # 测试各个API
    results = {}
    results['reddit'] = test_reddit_api(config)
    results['youtube'] = test_youtube_api(config)
    results['notion'] = test_notion_api(config)
    results['google_search'] = test_google_search_api(config)
    
    # 总结
    print("\n" + "="*50)
    print("📊 测试结果总结")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for api_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{api_name.upper()}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个API测试通过")
    
    if passed == total:
        print("🎉 所有API测试通过！应用应该可以正常运行。")
    else:
        print("⚠️  部分API测试失败，请检查配置和网络连接。")
        print("\n💡 解决建议:")
        print("1. 检查API密钥是否正确")
        print("2. 确认网络连接正常")
        print("3. 验证API配额是否充足")
        print("4. 检查API服务是否启用")

if __name__ == "__main__":
    main()
