# 🎮 H5游戏关键词猎手 - 独立运行指南

## 🚀 快速启动

### 方法1：双击启动（推荐）
1. 双击 `启动应用.bat` 文件
2. 等待应用启动（约10-30秒）
3. 自动打开浏览器访问 http://localhost:8501

### 方法2：命令行启动
```bash
# 在项目目录下执行
streamlit run main.py --server.port 8501
```

## 📊 监控运行状态

### 实时状态查看
- 双击 `查看状态.bat` 文件
- 选择相应选项查看应用状态

### 状态监控器
- 双击 `查看状态.bat` → 选择 `[3] 启动状态监控`
- 或直接运行: `python monitor.py`

## 📁 重要文件说明

### 启动文件
- `启动应用.bat` - 主启动脚本（推荐使用）
- `start.bat` - 备用启动脚本

### 监控文件
- `查看状态.bat` - 状态查看工具
- `monitor.py` - 状态监控脚本
- `app_monitor.log` - 运行日志文件
- `app_status.json` - 状态记录文件

### 配置文件
- `config.json` - API配置文件
- `setup_config.py` - 配置向导

## 🔍 运行状态说明

### 正常运行标志
- ✅ 浏览器能正常访问 http://localhost:8501
- ✅ 端口8501被占用
- ✅ 有Python进程在运行
- ✅ 应用界面显示"运行中"状态

### 异常状态处理
- ❌ 无法访问网页 → 检查应用是否启动
- ❌ 端口被占用 → 重启应用或更换端口
- ❌ Python错误 → 检查依赖包安装

## 🛠️ 常见问题解决

### 1. 应用无法启动
```bash
# 检查Python安装
python --version

# 检查依赖包
pip install -r requirements.txt

# 重新配置API
python setup_config.py
```

### 2. 端口冲突
- 修改启动脚本中的端口号（如8502、8503）
- 或停止占用8501端口的其他程序

### 3. API连接失败
- 检查网络连接
- 验证API密钥是否正确
- 查看config.json配置文件

## 📈 查看运行进度

### 方法1：应用界面
- 访问 http://localhost:8501
- 查看左侧边栏的状态信息
- 在"仪表板"标签页查看统计数据

### 方法2：日志文件
- 查看 `app_monitor.log` 文件
- 使用状态查看器的日志功能

### 方法3：状态文件
- 查看 `app_status.json` 文件
- 包含最新的运行状态信息

## 🔄 日常使用流程

### 启动应用
1. 双击 `启动应用.bat`
2. 等待启动完成
3. 浏览器自动打开应用

### 使用应用
1. 点击"验证配置"检查API状态
2. 点击"立即收集一次"获取关键词
3. 在各个标签页查看数据
4. 点击"同步到Notion"保存数据

### 监控状态
1. 双击 `查看状态.bat`
2. 选择相应功能查看状态
3. 可启动实时监控器

### 停止应用
- 关闭启动窗口
- 或按 Ctrl+C 停止

## 📞 技术支持

如果遇到问题：
1. 查看 `app_monitor.log` 日志文件
2. 检查 `app_status.json` 状态文件
3. 运行 `python test_apis.py` 诊断API问题
4. 重新运行 `python setup_config.py` 配置API

## 🎯 性能优化建议

- 定期清理日志文件（app_monitor.log）
- 监控系统资源使用情况
- 根据需要调整收集频率
- 定期备份配置文件
