@echo off
chcp 65001 >nul
title 🎮 H5游戏关键词猎手 - 运行监控台
color 0A

echo.
echo ========================================
echo    🎮 H5游戏关键词猎手 启动器
echo ========================================
echo.
echo 📍 应用地址: http://localhost:8501
echo 📊 运行状态: 此窗口显示实时日志
echo 🛑 停止应用: 按 Ctrl+C 或关闭此窗口
echo 🔄 重启应用: 关闭后重新双击此文件
echo.
echo ========================================
echo.

:: 切换到脚本所在目录
cd /d "%~dp0"

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Python，请先安装Python
    echo 📥 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 检查配置文件
if not exist "config.json" (
    echo ❌ 错误: 未找到config.json配置文件
    echo 🔧 请运行: python setup_config.py 来配置API密钥
    pause
    exit /b 1
)

:: 启动应用
echo ✅ 正在启动应用...
echo.
echo 📝 运行日志:
echo ----------------------------------------

python -m streamlit run main.py --server.port 8501 --server.headless true --server.runOnSave true

echo.
echo ----------------------------------------
echo 📴 应用已停止运行
echo.
pause
