@echo off
title H5游戏关键词猎手 - 状态查看器
color 0B
cd /d "%~dp0"

:MAIN_MENU
cls
echo.
echo ========================================
echo    🎮 H5游戏关键词猎手 状态查看器
echo ========================================
echo.
echo 请选择操作:
echo.
echo [1] 查看应用运行状态
echo [2] 查看实时日志
echo [3] 启动状态监控
echo [4] 打开应用网页
echo [5] 重启应用
echo [0] 退出
echo.
echo ========================================
echo.
set /p choice=请输入选项 (0-5): 

if "%choice%"=="1" goto CHECK_STATUS
if "%choice%"=="2" goto VIEW_LOGS
if "%choice%"=="3" goto START_MONITOR
if "%choice%"=="4" goto OPEN_WEB
if "%choice%"=="5" goto RESTART_APP
if "%choice%"=="0" goto EXIT
goto MAIN_MENU

:CHECK_STATUS
cls
echo.
echo 📊 检查应用状态...
echo ========================================
echo.

:: 检查端口是否被占用
netstat -an | findstr ":8501" >nul
if %errorlevel% equ 0 (
    echo ✅ 端口8501正在使用中 - 应用可能正在运行
) else (
    echo ❌ 端口8501未被占用 - 应用可能已停止
)

:: 检查Python进程
tasklist /fi "imagename eq python.exe" | findstr "python.exe" >nul
if %errorlevel% equ 0 (
    echo ✅ 发现Python进程正在运行
    echo.
    echo 📋 Python进程列表:
    tasklist /fi "imagename eq python.exe" /fo table
) else (
    echo ❌ 未发现Python进程
)

:: 检查状态文件
if exist "app_status.json" (
    echo.
    echo 📄 最新状态记录:
    type app_status.json
) else (
    echo.
    echo ⚠️ 未找到状态记录文件
)

echo.
echo ========================================
pause
goto MAIN_MENU

:VIEW_LOGS
cls
echo.
echo 📝 查看运行日志...
echo ========================================
echo.

if exist "app_monitor.log" (
    echo 📄 最近的日志记录:
    echo.
    powershell "Get-Content 'app_monitor.log' | Select-Object -Last 20"
) else (
    echo ⚠️ 未找到日志文件
    echo 💡 提示: 请先启动状态监控来生成日志
)

echo.
echo ========================================
pause
goto MAIN_MENU

:START_MONITOR
cls
echo.
echo 🔍 启动状态监控...
echo ========================================
echo.
echo 💡 监控器将在新窗口中运行
echo 🛑 关闭监控窗口即可停止监控
echo.
start "状态监控器" cmd /k "python monitor.py"
echo ✅ 监控器已启动
echo.
pause
goto MAIN_MENU

:OPEN_WEB
cls
echo.
echo 🌐 打开应用网页...
echo ========================================
echo.
start http://localhost:8501
echo ✅ 已在浏览器中打开应用
echo.
pause
goto MAIN_MENU

:RESTART_APP
cls
echo.
echo 🔄 重启应用...
echo ========================================
echo.
echo 正在停止现有进程...
taskkill /f /im python.exe >nul 2>&1
timeout /t 3 >nul

echo 正在启动应用...
start "H5游戏关键词猎手" cmd /k "启动应用.bat"
echo ✅ 应用重启完成
echo.
pause
goto MAIN_MENU

:EXIT
echo.
echo 👋 再见！
timeout /t 2 >nul
exit
