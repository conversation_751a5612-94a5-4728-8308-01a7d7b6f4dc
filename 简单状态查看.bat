@echo off
title H5游戏关键词猎手 - 状态查看
cd /d "%~dp0"

echo.
echo ========================================
echo    H5游戏关键词猎手 状态查看器
echo ========================================
echo.

echo 正在检查应用状态...
echo.

:: 检查端口是否被占用
echo [1] 检查端口8501状态:
netstat -an | findstr ":8501" >nul
if %errorlevel% equ 0 (
    echo     ✓ 端口8501正在使用中 - 应用正在运行
) else (
    echo     × 端口8501未被占用 - 应用可能已停止
)

echo.
echo [2] 检查Python进程:
tasklist /fi "imagename eq python.exe" | findstr "python.exe" >nul
if %errorlevel% equ 0 (
    echo     ✓ 发现Python进程正在运行
) else (
    echo     × 未发现Python进程
)

echo.
echo [3] 应用访问地址:
echo     http://localhost:8501

echo.
echo [4] 可用操作:
echo     - 双击 "启动应用.bat" 启动应用
echo     - 在浏览器中访问上述地址
echo     - 按任意键退出此窗口

echo.
echo ========================================
pause >nul
